#!/usr/bin/env node

/**
 * Script to increase user balance by email
 * Usage: node increase_balance.js <email> <amount>
 * Example: node increase_balance.js <EMAIL> 1000
 */

const mongoose = require('mongoose');
const User = require('./src/models/User');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

async function increaseUserBalance(email, amount) {
  try {
    // Connect to MongoDB
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB successfully');

    // Validate inputs
    if (!email || !amount) {
      throw new Error('Email and amount are required');
    }

    const balanceAmount = parseFloat(amount);
    if (isNaN(balanceAmount)) {
      throw new Error('Amount must be a valid number');
    }

    // Find user by email
    console.log(`🔍 Looking for user with email: ${email}`);
    const user = await User.findOne({ email: email });

    if (!user) {
      throw new Error(`User with email ${email} not found`);
    }

    // Get current balance
    const currentBalance = user.balance || 0;
    console.log(`💰 Current balance: ${currentBalance}`);

    // Update balance
    user.balance = currentBalance + balanceAmount;
    await user.save();

    console.log(`✅ Balance updated successfully!`);
    console.log(`📧 User: ${user.email}`);
    console.log(`👤 Name: ${user.name || 'N/A'}`);
    console.log(`💰 Previous balance: ${currentBalance}`);
    console.log(`➕ Amount added: ${balanceAmount}`);
    console.log(`💰 New balance: ${user.balance}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('🔌 MongoDB connection closed');
    process.exit(0);
  }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length !== 2) {
  console.log('❌ Usage: node increase_balance.js <email> <amount>');
  console.log('📝 Example: node increase_balance.js <EMAIL> 1000');
  process.exit(1);
}

const [email, amount] = args;

// Run the script
increaseUserBalance(email, amount);
